{"id": "692bf768-1830-4937-8b73-863de4b940d1", "rows_written": 172, "rows_read": 387, "storage_bytes_used": 94208, "write_requests_delegated": 0, "current_frame_no": 116, "top_query_threshold": 4, "top_queries": [{"rows_written": 3, "rows_read": 1, "query": "CREATE TABLE promocodes (id PRIMARY KEY DEFAULT (lower (hex (randomblob (16)))), text TEXT NOT NULL, kind TEXT NOT NULL DEFAULT 'sale', value INTEGER NOT NULL, start_date TEXT, end_date TEXT, activations INTEGER NOT NULL DEFAULT 0, activations_max INTEGER, hidden BOOLEAN NOT NULL DEFAULT FALSE);"}, {"rows_written": 3, "rows_read": 1, "query": "CREATE TABLE uploaded_medias (bot_username TEXT NOT NULL, media_name TEXT NOT NULL, media_file_id TEXT NOT NULL, PRIMARY KEY (bot_username, media_name));"}, {"rows_written": 3, "rows_read": 1, "query": "CREATE TABLE users (id BIGINT PRIMARY KEY, username TEXT, path TEXT, cart TEXT, balance REAL, pending_invoice_id BIGINT, is_admin BOOLEAN, active_promocode_id TEXT REFERENCES promocodes (id), referred_by BIGINT);"}, {"rows_written": 4, "rows_read": 2, "query": "CREATE TABLE link_product_data (id INTEGER PRIMARY KEY AUTOINCREMENT, product_id TEXT NOT NULL, sold BOOLEAN NOT NULL DEFAULT FALSE, model_name TEXT NOT NULL, medias TEXT NOT NULL, description TEXT NOT NULL, link TEXT NOT NULL, FOREIGN KEY (product_id) REFERENCES products (id));"}, {"rows_written": 4, "rows_read": 4, "query": "DELETE FROM associated_messages WHERE primary_message_id = ? AND user_id = ?;"}, {"rows_written": 8, "rows_read": 0, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('root', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"🗂 Канал-каталог анкет\",\n            \"url\": \"https://example.com\"\n        },\n        {\n            \"btn\": \"🔐 Пак кружков (обезличенные)\",\n            \"product_category\": \"pack\"\n        },\n        {\n            \"btn\": \"🧚🏻‍♀️ Модели классика\",\n            \"dir\": \"classic\"\n        },\n        {\n            \"btn\": \"🔞 Модели onlyfans\",\n            \"dir\": \"onlyfans\"\n        },\n        {\n            \"btn\": \"🧖🏻‍♀️ Модели домашние\",\n            \"dir\": \"home\"\n        }\n    ]'), ('classic', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"classic-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"classic-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"classic-big\"\n        }\n    ]'), ('onlyfans', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"onlyfans-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"onlyfans-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"onlyfans-big\"\n        }\n    ]'), ('home', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"home-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"home-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"home-big\"\n        }\n    ]');"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM `catalog` ORDER BY `id` ASC LIMIT 100 OFFSET 0;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL;"}, {"rows_written": 20, "rows_read": 0, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('pack', 'category', 'accounts', NULL, NULL), ('classic-small', 'category', 'accounts', NULL, NULL), ('classic-medium', 'category', 'accounts', NULL, NULL), ('classic-big', 'category', 'accounts', NULL, NULL), ('onlyfans-small', 'category', 'accounts', NULL, NULL), ('onlyfans-medium', 'category', 'accounts', NULL, NULL), ('onlyfans-big', 'category', 'accounts', NULL, NULL), ('home-small', 'category', 'accounts', NULL, NULL), ('home-medium', 'category', 'accounts', NULL, NULL), ('home-big', 'category', 'accounts', NULL, NULL);"}], "slowest_query_threshold": 2, "slowest_queries": [{"elapsed_ms": 2, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 2, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 2, "rows_read": 0}, {"elapsed_ms": 2, "query": "SELECT * FROM link_product_data WHERE product_id = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT * FROM products WHERE category = ? AND enabled;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT * FROM users WHERE id = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT hash FROM migrations WHERE name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 4, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 9, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)", "rows_written": 3, "rows_read": 1}, {"elapsed_ms": 36, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;", "rows_written": 0, "rows_read": 14}], "embedded_replica_frames_replicated": 0, "query_count": 279, "query_latency": 275982}